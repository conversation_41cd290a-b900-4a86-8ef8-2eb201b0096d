/**
 * Actions 管理
 * 简化的action管理系统，只维护翻译相关的actions
 */

// 导入翻译actions
import { getTranslationActions } from './translationActions'

// Action基础类型定义
export interface BaseAction {
  name: string;
  onHandle: (...args: any[]) => void;
  description?: string;
}

/**
 * 获取所有可用的actions
 */
export const getAllActions = (): BaseAction[] => {
  return getTranslationActions()
}

/**
 * 获取用于ChatUI的actions
 */
export const getActionsForChatUI = (): BaseAction[] => {
  return getAllActions()
}

// 默认导出所有actions（用于ChatUI）
export default getActionsForChatUI
