/**
 * 翻译相关的Action函数
 */

import type { BaseAction } from './index'

/**
 * 处理翻译API返回值的action函数
 * 这个函数封装了原本在批量翻译处理中的响应处理逻辑
 */
export const createInsertDomAction = (): BaseAction => {
  return {
    name: 'insertDom',
    description: '处理翻译API返回值，更新页面翻译图标',
    onHandle: (response: any, group: any[]) => {
      if (response?.code === '0') {
        // 使用'###'分割批量翻译结果
        const regex = /翻译结果：(.*?)(?=[^\\]")/;
        const match = response.result.match(regex);
        if (match) {
          const translation = match[1].trim();
          const results = response.result.split('###');

          group.forEach((item: any, index: number) => {
            if (results[index]) {
              // 通过Chrome消息传递机制调用content script中的更新函数
              chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
                if (tabs[0]) {
                  // chrome.tabs.sendMessage(tabs[0].id, {
                  //   type: 'UPDATE_TRANSLATION_ICON',
                  //   data: {
                  //     iconId: item.icon?.getAttribute?.('data-translate-id') || item.id,
                  //     translation: results[index],
                  //     paragraphId: item.paragraph?.getAttribute?.('data-paragraph-id'),
                  //     htmlStructure: item.htmlStructure
                  //   }
                  // });
                }
              });
            }
          });
        } else {
          // 处理错误情况
          console.error('翻译失败:', response?.error || '未知错误');
        }
      }
    };
  };

  /**
   * 获取所有翻译相关的actions
   */
  export const getTranslationActions = (): BaseAction[] => {
    return [
      createInsertDomAction()
    ];
  };